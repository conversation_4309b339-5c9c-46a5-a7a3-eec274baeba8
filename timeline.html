<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Our Journey</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link rel="stylesheet" href="assets/css/base.css" />
  <link rel="stylesheet" href="assets/css/timeline.css" />
</head>

<body class="timeline-page">
  <div class="transition-layer"></div>
  <header class="header">
    <div class="container">
      <h1 class="display-romance">Our Journey</h1>
      <div class="scroll-hint">Scroll through our memories</div>
    </div>
  </header>

  <section class="timeline">
    <aside class="axis" aria-label="Timeline dates"></aside>
    <div class="entries" aria-live="polite"></div>
  </section>

  <div id="bottom-sentinel" class="bottom-sentinel" aria-hidden="true"></div>

  <footer class="bottom-cta" role="contentinfo" aria-label="Navigate to proposal">
    <div class="container">
      <button id="to-proposal-btn" class="btn">Continue to the Proposal</button>
    </div>
  </footer>

  <script type="module" src="assets/js/audio-manager.js"></script>
  <script type="module" src="assets/js/timeline.js"></script>
  <noscript>
    <div class="container" style="padding: 16px 0 100px;">
      JavaScript is required to view the timeline.
    </div>
  </noscript>
  <script nomodule>
    (function ()
    {
      function $(sel) { return document.querySelector(sel); }
      function createTick(text) { var d = document.createElement('div'); d.className = 'tick revealed'; d.textContent = text; return d; }
      function createEntry(item)
      {
        var wrapper = document.createElement('article'); wrapper.className = 'entry';
        var card = document.createElement('div'); card.className = 'card revealed';
        var img = document.createElement('img'); img.src = item.image; img.alt = item.title || 'Memory photo';
        img.onerror = function () { img.src = 'https://images.unsplash.com/photo-1517524206127-48bbd363f3ae?q=80&w=1600&auto=format&fit=crop'; };
        var content = document.createElement('div'); content.className = 'content';
        var date = document.createElement('div'); date.className = 'date'; date.textContent = item.date || '';
        var title = document.createElement('h3'); title.className = 'title'; title.textContent = item.title || '';
        var p = document.createElement('p'); p.className = 'desc'; p.textContent = item.text || '';
        content.appendChild(date); content.appendChild(title); content.appendChild(p);
        card.appendChild(img); card.appendChild(content); wrapper.appendChild(card); return wrapper;
      }
      function xhrJSON(url, cb) { var r = new XMLHttpRequest(); r.open('GET', url, true); r.onreadystatechange = function () { if (r.readyState === 4) { if (r.status >= 200 && r.status < 300) { try { cb(null, JSON.parse(r.responseText)); } catch (e) { cb(e); } } else { cb(new Error('HTTP ' + r.status)); } } }; r.send(); }
      document.addEventListener('DOMContentLoaded', function ()
      {
        var axis = $('.axis'); var entriesEl = $('.entries'); var header = $('.header'); var cta = document.querySelector('.bottom-cta');
        xhrJSON('data/timeline.json', function (err, data)
        {
          if (err) { entriesEl.innerHTML = '<p>Unable to load our story right now. Please refresh.</p>'; return; }
          (data.items || []).forEach(function (item) { axis.appendChild(createTick(item.date)); entriesEl.appendChild(createEntry(item)); });
          axis.addEventListener('click', function (e) { var t = e.target; if (!t.classList.contains('tick')) return; var idx = [].indexOf.call(axis.children, t); var entry = entriesEl.children[idx]; if (entry) { var headerH = (header && header.offsetHeight) || 60; var top = entry.offsetTop - headerH - 20; window.scrollTo(0, top); t.style.background = 'rgba(214, 51, 108, 0.2)'; setTimeout(function () { t.style.background = ''; }, 300); } });
          if (cta)
          {
            var onScroll = function () { var nearBottom = window.innerHeight + window.scrollY >= document.body.offsetHeight - 10; if (nearBottom) { cta.classList.add('show'); } else { cta.classList.remove('show'); } };
            window.addEventListener('scroll', onScroll, { passive: true }); onScroll();
          }
        });
      });
    })();
  </script>
</body>

</html>