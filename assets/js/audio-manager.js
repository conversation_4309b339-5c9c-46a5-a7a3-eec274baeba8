// Global audio manager for persistent background music across pages
class AudioManager {
  constructor() {
    this.audio = null;
    this.isPlaying = false;
    this.currentTime = 0;
    this.volume = 0.3; // Default volume (30%)
    this.songPath = 'song/Can\'t Help Falling in Love - Haley Reinhart.mp3';
    this.storageKey = 'proposalWebsiteAudio';
    
    // Initialize from localStorage if available
    this.loadState();
    this.initializeAudio();
  }

  initializeAudio() {
    if (!this.audio) {
      this.audio = new Audio(this.songPath);
      this.audio.loop = true; // Enable looping
      this.audio.volume = this.volume;
      this.audio.preload = 'auto';
      
      // Event listeners
      this.audio.addEventListener('timeupdate', () => {
        this.currentTime = this.audio.currentTime;
        this.saveState();
      });
      
      this.audio.addEventListener('loadeddata', () => {
        // Restore playback position if we have one
        if (this.currentTime > 0) {
          this.audio.currentTime = this.currentTime;
        }
        
        // Auto-play if it was playing before
        if (this.isPlaying) {
          this.play();
        }
      });
      
      this.audio.addEventListener('error', (e) => {
        console.warn('Audio error:', e);
      });
    }
  }

  play() {
    if (this.audio) {
      const playPromise = this.audio.play();
      if (playPromise !== undefined) {
        playPromise
          .then(() => {
            this.isPlaying = true;
            this.saveState();
          })
          .catch((error) => {
            console.warn('Audio play failed:', error);
            // Handle autoplay restrictions
            this.showPlayButton();
          });
      }
    }
  }

  pause() {
    if (this.audio) {
      this.audio.pause();
      this.isPlaying = false;
      this.saveState();
    }
  }

  toggle() {
    if (this.isPlaying) {
      this.pause();
    } else {
      this.play();
    }
  }

  setVolume(vol) {
    this.volume = Math.max(0, Math.min(1, vol));
    if (this.audio) {
      this.audio.volume = this.volume;
    }
    this.saveState();
  }

  saveState() {
    const state = {
      isPlaying: this.isPlaying,
      currentTime: this.currentTime,
      volume: this.volume
    };
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(state));
    } catch (e) {
      // Handle localStorage errors silently
    }
  }

  loadState() {
    try {
      const saved = localStorage.getItem(this.storageKey);
      if (saved) {
        const state = JSON.parse(saved);
        this.isPlaying = state.isPlaying || false;
        this.currentTime = state.currentTime || 0;
        this.volume = state.volume || 0.3;
      }
    } catch (e) {
      // Handle localStorage errors silently
    }
  }

  // Show a play button for user interaction (required for autoplay policies)
  showPlayButton() {
    if (document.querySelector('.audio-play-prompt')) return; // Already shown
    
    const playPrompt = document.createElement('div');
    playPrompt.className = 'audio-play-prompt';
    playPrompt.innerHTML = `
      <div class="audio-prompt-content">
        <div class="audio-prompt-icon">🎵</div>
        <p>Click to play background music</p>
        <button class="audio-prompt-btn">Play Music</button>
      </div>
    `;
    
    // Add styles
    playPrompt.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: rgba(214, 51, 108, 0.95);
      color: white;
      padding: 15px 20px;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.3);
      z-index: 1000;
      font-family: inherit;
      backdrop-filter: blur(10px);
      animation: slideInRight 0.3s ease-out;
    `;
    
    const btn = playPrompt.querySelector('.audio-prompt-btn');
    btn.style.cssText = `
      background: white;
      color: var(--primary, #d6336c);
      border: none;
      padding: 8px 16px;
      border-radius: 6px;
      cursor: pointer;
      font-weight: 600;
      margin-top: 8px;
      transition: transform 0.2s ease;
    `;
    
    btn.addEventListener('click', () => {
      this.play();
      playPrompt.remove();
    });
    
    btn.addEventListener('mouseenter', () => {
      btn.style.transform = 'scale(1.05)';
    });
    
    btn.addEventListener('mouseleave', () => {
      btn.style.transform = 'scale(1)';
    });
    
    document.body.appendChild(playPrompt);
    
    // Auto-hide after 10 seconds
    setTimeout(() => {
      if (playPrompt.parentNode) {
        playPrompt.style.animation = 'slideOutRight 0.3s ease-in forwards';
        setTimeout(() => playPrompt.remove(), 300);
      }
    }, 10000);
  }

  // Create audio controls UI
  createControls() {
    if (document.querySelector('.audio-controls')) return; // Already exists
    
    const controls = document.createElement('div');
    controls.className = 'audio-controls';
    controls.innerHTML = `
      <button class="audio-toggle" title="Toggle music">
        <span class="audio-icon">${this.isPlaying ? '⏸️' : '▶️'}</span>
      </button>
      <input type="range" class="audio-volume" min="0" max="100" value="${this.volume * 100}" title="Volume">
    `;
    
    // Add styles
    controls.style.cssText = `
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: rgba(255, 255, 255, 0.95);
      padding: 10px 15px;
      border-radius: 25px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.15);
      z-index: 1000;
      display: flex;
      align-items: center;
      gap: 10px;
      backdrop-filter: blur(10px);
      transition: opacity 0.3s ease;
    `;
    
    const toggleBtn = controls.querySelector('.audio-toggle');
    toggleBtn.style.cssText = `
      background: none;
      border: none;
      font-size: 18px;
      cursor: pointer;
      padding: 5px;
      border-radius: 50%;
      transition: background 0.2s ease;
    `;
    
    const volumeSlider = controls.querySelector('.audio-volume');
    volumeSlider.style.cssText = `
      width: 80px;
      height: 4px;
      background: #ddd;
      border-radius: 2px;
      outline: none;
      cursor: pointer;
    `;
    
    // Event listeners
    toggleBtn.addEventListener('click', () => {
      this.toggle();
      toggleBtn.querySelector('.audio-icon').textContent = this.isPlaying ? '⏸️' : '▶️';
    });
    
    volumeSlider.addEventListener('input', (e) => {
      this.setVolume(e.target.value / 100);
    });
    
    toggleBtn.addEventListener('mouseenter', () => {
      toggleBtn.style.background = 'rgba(214, 51, 108, 0.1)';
    });
    
    toggleBtn.addEventListener('mouseleave', () => {
      toggleBtn.style.background = 'none';
    });
    
    document.body.appendChild(controls);
    
    // Auto-hide controls after inactivity
    let hideTimeout;
    const resetHideTimeout = () => {
      clearTimeout(hideTimeout);
      controls.style.opacity = '1';
      hideTimeout = setTimeout(() => {
        controls.style.opacity = '0.7';
      }, 3000);
    };
    
    controls.addEventListener('mouseenter', () => {
      clearTimeout(hideTimeout);
      controls.style.opacity = '1';
    });
    
    controls.addEventListener('mouseleave', resetHideTimeout);
    
    resetHideTimeout();
  }
}

// Create global instance
window.audioManager = window.audioManager || new AudioManager();

// Export for module usage
export default window.audioManager;
