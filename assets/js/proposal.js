import { mountFloatingHearts } from './utils.js';
import audioManager from './audio-manager.js';

window.addEventListener('DOMContentLoaded', () => {
  mountFloatingHearts(document.body, 26);

  // Initialize audio manager and create controls
  audioManager.createControls();

  const yesBtn = document.getElementById('yes-btn');
  const body = document.body;

  // Make it impossible to say no: only a YES button exists, it grows when hovered
  if (yesBtn) {
    yesBtn.addEventListener('mouseenter', () => {
      yesBtn.style.transform = 'scale(1.08)';
    });
    yesBtn.addEventListener('mouseleave', () => {
      yesBtn.style.transform = 'scale(1)';
    });
    yesBtn.addEventListener('click', () => {
      // Confetti-ish hearts burst
      const burst = document.createElement('div');
      burst.style.position = 'fixed';
      burst.style.inset = '0';
      burst.style.pointerEvents = 'none';
      document.body.appendChild(burst);
      for (let i = 0; i < 40; i++) {
        const h = document.createElement('div');
        h.className = 'heart';
        const size = 10 + Math.random() * 20;
        h.style.width = h.style.height = size + 'px';
        h.style.left = '50%';
        h.style.top = '50%';
        h.style.opacity = '0.8';
        const dx = (Math.random() - 0.5) * 120;
        const dy = (Math.random() - 0.5) * 120;
        h.animate([
          { transform: `translate(-50%, -50%) rotate(45deg)`, offset: 0 },
          { transform: `translate(calc(-50% + ${dx}vmin), calc(-50% + ${dy}vmin)) rotate(45deg)` }
        ], { duration: 1000 + Math.random() * 1000, easing: 'cubic-bezier(.2,.7,.2,1)' });
        burst.appendChild(h);
      }

      setTimeout(() => {
        burst.remove();
        alert('Yay! Love you forever ❤️');
      }, 1400);
    });
  }
});

