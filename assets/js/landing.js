import { mountFloatingHearts } from './utils.js';

window.addEventListener('DOMContentLoaded', () => {
  const cleanup = mountFloatingHearts(document.body, 20);
  const btn = document.getElementById('open-envelope');
  btn?.addEventListener('click', () => {
    // Small click animation
    btn.style.transition = 'transform .35s ease';
    btn.style.transform = 'translateY(-6px) scale(1.04)';
    setTimeout(() => {
      window.location.href = 'timeline.html';
    }, 380);
  });
});

