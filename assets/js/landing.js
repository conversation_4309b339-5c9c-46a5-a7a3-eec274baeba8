import { mountFloatingHearts } from './utils.js';
import audioManager from './audio-manager.js';

window.addEventListener('DOMContentLoaded', () => {
  const cleanup = mountFloatingHearts(document.body, 20);

  // Initialize audio manager and create controls
  audioManager.createControls();

  // Try to start playing music (will show prompt if autoplay is blocked)
  setTimeout(() => {
    audioManager.play();
  }, 1000); // Small delay to let page settle

  const btn = document.getElementById('open-envelope');
  btn?.addEventListener('click', () => {
    // Small click animation
    btn.style.transition = 'transform .35s ease';
    btn.style.transform = 'translateY(-6px) scale(1.04)';
    setTimeout(() => {
      window.location.href = 'timeline.html';
    }, 380);
  });
});

