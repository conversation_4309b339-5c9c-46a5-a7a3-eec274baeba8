// Utility: floating hearts background and intersection reveal
export function mountFloatingHearts(container = document.body, count = 18) {
  const wrapper = document.createElement('div');
  wrapper.className = 'hearts-bg';
  container.appendChild(wrapper);
  for (let i = 0; i < count; i++) {
    const h = document.createElement('div');
    h.className = 'heart';
    const left = Math.random() * 100;
    const size = 8 + Math.random() * 16;
    const delay = Math.random() * 6;
    const duration = 8 + Math.random() * 10;
    h.style.left = left + 'vw';
    h.style.bottom = (-10 - Math.random() * 40) + 'vh';
    h.style.width = size + 'px';
    h.style.height = size + 'px';
    h.style.animation = `floatUp ${duration}s linear ${delay}s infinite`;
    wrapper.appendChild(h);
  }
  return () => wrapper.remove();
}

export function revealOnView(selector, { root = null, rootMargin = '0px 0px -10% 0px', threshold = 0.1 } = {}) {
  const items = Array.from(document.querySelectorAll(selector));
  if (!items.length) return () => {};

  // Helper: immediate check based on bounding rect (fallback for buggy IO on some mobiles)
  const rectCheckReveal = () => {
    const vh = window.innerHeight || document.documentElement.clientHeight || 0;
    items.forEach(el => {
      if (el.classList.contains('revealed')) return;
      const r = el.getBoundingClientRect();
      const visible = r.top < vh * 0.9 && r.bottom > vh * -0.1;
      if (visible) el.classList.add('revealed');
    });
  };

  // If IO not available, use rect check on scroll/resize
  if (!('IntersectionObserver' in window)) {
    rectCheckReveal();
    const onScroll = () => rectCheckReveal();
    window.addEventListener('scroll', onScroll, { passive: true });
    window.addEventListener('resize', onScroll);
    return () => {
      window.removeEventListener('scroll', onScroll);
      window.removeEventListener('resize', onScroll);
    };
  }

  // Use IO when available
  const io = new IntersectionObserver(entries => {
    entries.forEach(e => {
      if (e.isIntersecting) {
        e.target.classList.add('revealed');
        io.unobserve(e.target);
      }
    });
  }, { root, rootMargin, threshold });
  items.forEach(el => io.observe(el));

  // Safety: if nothing is revealed shortly after load (some iOS bugs), also wire a rect-check
  let safetyTimer = setTimeout(() => {
    const anyRevealed = items.some(el => el.classList.contains('revealed'));
    if (!anyRevealed) {
      rectCheckReveal();
      const onScroll = () => rectCheckReveal();
      window.addEventListener('scroll', onScroll, { passive: true });
      window.addEventListener('resize', onScroll);
      // Clear listeners once all revealed
      const maybeDetach = () => {
        if (items.every(el => el.classList.contains('revealed'))) {
          window.removeEventListener('scroll', onScroll);
          window.removeEventListener('resize', onScroll);
        }
      };
      window.addEventListener('scroll', maybeDetach, { passive: true });
      window.addEventListener('resize', maybeDetach);
    }
  }, 800);

  return () => {
    clearTimeout(safetyTimer);
    io.disconnect();
  };
}

export function smoothScrollTo(el) {
  const top = typeof el === 'number' ? el : (el?.getBoundingClientRect().top + window.scrollY);
  window.scrollTo({ top, behavior: 'smooth' });
}

