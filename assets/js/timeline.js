import { mountFloatingHearts, revealOnView, smoothScrollTo } from './utils.js';

function fitImageToCardWidthMaxHeight(imageEl, cardEl) {
  if (!imageEl) return;
  imageEl.style.width = '100%';
  imageEl.style.height = 'auto';
  imageEl.style.marginLeft = 'auto';
  imageEl.style.marginRight = 'auto';
}

async function loadTimelineData() {
  const res = await fetch('data/timeline.json');
  if (!res.ok) throw new Error('Failed to load timeline.json');
  return res.json();
}

function createTick(dateText) {
  const div = document.createElement('div');
  div.className = 'tick fade-in';
  div.textContent = dateText;
  return div;
}

function createEntry(item) {
  const wrapper = document.createElement('article');
  wrapper.className = 'entry';
  const card = document.createElement('div');
  card.className = 'card';
  const img = document.createElement('img');
  img.src = item.image;
  img.alt = item.title || 'Memory photo';
  img.onerror = () => { img.src = 'https://images.unsplash.com/photo-1517524206127-48bbd363f3ae?q=80&w=1600&auto=format&fit=crop'; };
  img.addEventListener('load', () => {
    fitImageToCardWidthMaxHeight(img, card);
  });
  // Re-fit on card resize to handle responsive layout changes
  if (typeof ResizeObserver !== 'undefined') {
    const resizeObserver = new ResizeObserver(() => fitImageToCardWidthMaxHeight(img, card));
    resizeObserver.observe(card);
  } else {
    const onResize = () => fitImageToCardWidthMaxHeight(img, card);
    window.addEventListener('resize', onResize);
  }
  const content = document.createElement('div');
  content.className = 'content';
  const date = document.createElement('div');
  date.className = 'date';
  date.textContent = item.date;
  const title = document.createElement('h3');
  title.className = 'title';
  title.textContent = item.title || '';
  const p = document.createElement('p');
  p.className = 'desc';
  p.textContent = item.text || '';

  content.append(date, title, p);
  card.append(img, content);
  wrapper.append(card);
  return wrapper;
}

// Auto-scroll/auto-navigation removed per new requirement

window.addEventListener('DOMContentLoaded', async () => {
  mountFloatingHearts(document.body, 14);

  const axis = document.querySelector('.axis');
  const entriesEl = document.querySelector('.entries');
  const scrollHint = document.querySelector('.scroll-hint');

  try {
    const data = await loadTimelineData();

    data.items.forEach((item, idx) => {
      axis.appendChild(createTick(item.date));
      const entryEl = createEntry(item);
      entriesEl.appendChild(entryEl);
    });

    revealOnView('.tick');
    revealOnView('.card');

    // Smooth scroll between entries when clicking on axis ticks
    axis.addEventListener('click', (e) => {
      const target = e.target;
      if (target.classList.contains('tick')) {
        const idx = Array.from(axis.children).indexOf(target);
        const entry = entriesEl.children[idx];
        if (entry) {
          // Account for header height and add some padding
          const headerHeight = document.querySelector('.header').offsetHeight || 60;
          const targetPosition = entry.offsetTop - headerHeight - 20;
          smoothScrollTo(targetPosition);

          // Add visual feedback
          target.style.background = 'rgba(214, 51, 108, 0.2)';
          setTimeout(() => {
            target.style.background = '';
          }, 300);
        }
      }
    });

    if (scrollHint) setTimeout(() => scrollHint.classList.add('fade'), 3000);

    const navBtn = document.getElementById('to-proposal-btn');
    if (navBtn) {
      navBtn.addEventListener('click', () => {
        const layer = document.querySelector('.transition-layer');
        if (layer) layer.classList.add('show');
        setTimeout(() => {
          window.location.href = 'proposal.html';
        }, 500);
      });
    }

    // Reveal the CTA only when reaching the bottom sentinel
    const sentinel = document.getElementById('bottom-sentinel');
    const cta = document.querySelector('.bottom-cta');
    if ('IntersectionObserver' in window && sentinel && cta) {
      const io = new IntersectionObserver(entries => {
        entries.forEach(e => {
          if (e.isIntersecting) {
            cta.classList.add('show');
          } else {
            cta.classList.remove('show');
          }
        });
      }, { root: null, threshold: 0.1 });
      io.observe(sentinel);
    } else if (cta) {
      // Fallback: show the button only near bottom
      const onScroll = () => {
        const nearBottom = window.innerHeight + window.scrollY >= document.body.offsetHeight - 10;
        cta.classList.toggle('show', nearBottom);
      };
      window.addEventListener('scroll', onScroll, { passive: true });
      onScroll();
    }
  } catch (err) {
    console.error(err);
    entriesEl.innerHTML = '<p>Unable to load our story right now. Please refresh.</p>';
  }
});

