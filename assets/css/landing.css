/* Landing page styles */
.landing { position: relative; min-height: 100dvh; display: grid; place-items: center; padding: 40px 0; }

.cta { position: relative; text-align: center; z-index: 2; }
.cta h1 { font-size: clamp(32px, 6vw, 56px); margin: 0 0 10px; }
.cta p { margin: 6px 0 26px; color: var(--ink-700); }

/* Envelope button */
.envelope-btn { position: relative; width: 180px; height: 140px; background: linear-gradient(180deg, #fff, #ffe9ef); border-radius: 12px; box-shadow: var(--shadow-elev); border: 2px solid var(--rose-100); transition: transform .25s ease; }
.envelope-btn:hover { transform: translateY(-3px) scale(1.02); }

.envelope-flap { position: absolute; inset: 0; background: linear-gradient(135deg, var(--rose-100), #fff); clip-path: polygon(0 0, 100% 0, 50% 60%); border-top-left-radius: 12px; border-top-right-radius: 12px; }
.envelope-heart { position: absolute; left: 50%; top: 60%; transform: translate(-50%, -50%); width: 32px; height: 32px; background: var(--primary); transform-origin: center; border-radius: 6px; rotate: 45deg; box-shadow: 0 6px 20px rgba(214,51,108,.35); }
.envelope-heart::before, .envelope-heart::after { content: ''; position: absolute; width: 32px; height: 32px; background: var(--primary); border-radius: 50%; }
.envelope-heart::before { left: -16px; }
.envelope-heart::after { top: -16px; }

/* Decorative letter sheets */
.letter-sheet { position: absolute; width: 220px; height: 160px; background: #fff; border: 1px solid var(--rose-100); border-radius: var(--radius-md); box-shadow: var(--shadow-soft); transform: rotate(var(--r, -4deg)); animation: float 8s ease-in-out infinite; opacity: .9; }
.letter-sheet::after { content: ''; position: absolute; inset: 12px; border: 2px dashed var(--rose-100); border-radius: 10px; }
.letter-1 { left: 8%; top: 15%; --r: -6deg; animation-delay: .2s; }
.letter-2 { right: 10%; top: 20%; --r: 8deg; animation-delay: 1.4s; }
.letter-3 { left: 14%; bottom: 16%; --r: 5deg; animation-delay: .8s; }
.letter-4 { right: 14%; bottom: 12%; --r: -10deg; animation-delay: 2s; }

@keyframes float { 0%,100% { transform: translateY(0) rotate(var(--r)); } 50% { transform: translateY(-10px) rotate(calc(var(--r) + 2deg)); } }

.cta .subtle { font-size: 15px; opacity: .9; }

