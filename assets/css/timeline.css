/* Timeline page styles */
.timeline-page { min-height: 100dvh; }
.header { position: sticky; top: 0; background: linear-gradient(180deg, rgba(255,245,247,.92), rgba(255,245,247,.6)); backdrop-filter: blur(8px); border-bottom: 1px solid var(--rose-100); z-index: 5; }
.header .container { display: flex; align-items: center; justify-content: space-between; padding: 14px 0; }
.header h1 { margin: 0; font-size: 22px; }

.timeline {
  position: relative;
  z-index: 1; /* Ensure content sits above floating hearts on mobile */
  width: min(1100px, 94vw);
  margin: 24px auto;
  display: flex;
  gap: 32px;
  align-items: flex-start;
}

/* Axis - Sticky sidebar */
.axis {
  position: sticky;
  top: 100px; /* Account for header height + some padding */
  width: 200px; /* Fixed width for the axis */
  min-width: 200px; /* Prevent shrinking */
  height: fit-content;
  max-height: calc(100vh - 120px); /* Full height minus header and padding */
  padding: 20px 0;
  overflow-y: auto; /* Allow scrolling if content is too tall */
  background: rgba(255, 245, 247, 0.95); /* Semi-transparent background */
  backdrop-filter: blur(8px); /* Blur effect for modern look */
  border-radius: 16px; /* Rounded edges */
  box-shadow: 0 4px 20px rgba(214, 51, 108, 0.1); /* Subtle shadow */
  flex-shrink: 0; /* Prevent the axis from shrinking */
}
.axis::before { content: ''; position: absolute; right: -16px; top: 20px; bottom: 20px; width: 2px; background: linear-gradient(var(--rose-200), var(--rose-400)); border-radius: 2px; }
.axis .tick {
  position: relative;
  padding: 12px 24px 12px 16px;
  margin: 20px 0;
  color: var(--ink-700);
  font-weight: 600;
  cursor: pointer;
  border-radius: 12px;
  transition: all 0.3s ease;
}
.axis .tick:hover {
  background: rgba(214, 51, 108, 0.1);
  color: var(--primary);
  transform: translateX(4px);
}
.axis .tick::after {
  content: '';
  position: absolute;
  right: -22px;
  top: 50%;
  transform: translateY(-50%);
  width: 10px;
  height: 10px;
  background: var(--white);
  border: 2px solid var(--primary);
  border-radius: 50%;
  box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}
.axis .tick:hover::after {
  transform: translateY(-50%) scale(1.2);
  box-shadow: 0 0 0 6px rgba(214, 51, 108, 0.2);
}

/* Entries - Now part of flexbox layout */
.entries {
  position: relative;
  padding: 20px 0;
  flex: 1; /* Take up remaining space */
  min-width: 0; /* Allow shrinking */
}
.timeline { padding-bottom: 90px; }
.entry { display: grid; grid-template-columns: 1fr; gap: 12px; margin: 28px 0 48px; }
.card { background: #fff; border: 1px solid var(--rose-100); border-radius: 16px; overflow: hidden; box-shadow: var(--shadow-soft); transform: translateY(10px); opacity: 0; transition: transform .6s ease, opacity .6s ease; container-type: inline-size; }
.card.revealed { transform: translateY(0); opacity: 1; }
.card img {
  width: 100%;
  height: auto;
  display: block;
  margin-left: auto;
  margin-right: auto;
}
.card .content { padding: 16px 18px 18px; }
.card .date { font-weight: 700; color: var(--primary); margin-bottom: 6px; letter-spacing: .3px; }
.card .title { font-family: 'Playfair Display', Georgia, serif; font-size: 22px; margin: 0 0 6px; }
.card .desc { color: var(--ink-700); margin: 0; }

/* Scroll hint */
.scroll-hint { text-align: center; color: var(--ink-700); margin: 10px 0 20px; font-size: 14px; opacity: 1; transition: opacity .6s ease; }
.scroll-hint.fade { opacity: 0.2; }

/* Transition layer (kept for future transitions but unused for auto scroll) */
.transition-layer { position: fixed; inset: 0; background: var(--rose-50); z-index: 20; opacity: 0; pointer-events: none; transition: opacity .6s ease; }
.transition-layer.show { opacity: 1; }

/* Bottom call-to-action button */
.bottom-cta { position: fixed; left: 0; right: 0; bottom: 0; width: 100%; background: linear-gradient(180deg, rgba(255,245,247,.2), rgba(255,245,247,1)); border-top: 1px solid var(--rose-100); padding: 12px 0; z-index: 6; opacity: 0; pointer-events: none; transform: translateY(8px); transition: opacity .35s ease, transform .35s ease; }
.bottom-cta.show { opacity: 1; pointer-events: auto; transform: translateY(0); }
.bottom-cta .container { display: flex; justify-content: center; }
.bottom-cta .btn { min-width: 260px; }

/* Bottom sentinel for reveal trigger */
.bottom-sentinel { height: 1px; }

/* Medium screens - reduce gap */
@media (max-width: 1200px) {
  .timeline {
    gap: 24px;
  }
  .axis {
    width: 180px;
    min-width: 180px;
  }
}

@media (max-width: 1000px) {
  .timeline {
    gap: 20px;
  }
  .axis {
    width: 160px;
    min-width: 160px;
  }
}

/* Small screens - stack layout */
@media (max-width: 820px) {
  .timeline {
    flex-direction: column;
    gap: 20px;
  }
  .entries {
    flex: none; /* Don't use flex sizing in column layout */
    width: 100%; /* Take full width */
    min-width: auto; /* Reset min-width */
  }
  .axis {
    position: relative;
    top: auto;
    width: 100%;
    min-width: auto;
    max-height: none;
    height: auto;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    padding: 16px;
  }
  .axis::before { display: none; }
  .axis .tick {
    padding: 8px 12px;
    margin: 0;
    background: var(--rose-100);
    border-radius: 20px;
    font-size: 14px;
  }
  .axis .tick::after { display: none; }
}
