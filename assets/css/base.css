/* Base styles and design tokens */
:root {
  --rose-50: #fff5f7;
  --rose-100: #ffe3ea;
  --rose-200: #ffc2cf;
  --rose-300: #ff9bb1;
  --rose-400: #ff7797;
  --rose-500: #e85a82;
  --rose-600: #d6336c; /* primary */
  --rose-700: #b22457;
  --rose-800: #7a183a;
  --gold-300: #ffd7a3;
  --gold-500: #f0b46b;
  --ink-900: #1b1b1b;
  --ink-700: #424242;
  --white: #ffffff;

  --bg: var(--rose-50);
  --text: var(--ink-900);
  --primary: var(--rose-600);
  --accent: var(--gold-500);

  --radius-lg: 18px;
  --radius-md: 12px;
  --radius-sm: 8px;
  --shadow-soft: 0 10px 30px rgba(214, 51, 108, 0.15);
  --shadow-elev: 0 20px 45px rgba(214, 51, 108, 0.25);
}

/* CSS Reset (modern minimal) */
*, *::before, *::after { box-sizing: border-box; }
html { -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; scroll-behavior: smooth; }
html, body { height: 100%; }
body { margin: 0; background: var(--bg); color: var(--text); }
img { max-width: 100%; display: block; }
a { color: inherit; text-decoration: none; }

/* Typography */
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@500;700&family=Inter:wght@400;500;600&display=swap');

body { font-family: Inter, system-ui, -apple-system, Segoe UI, Roboto, sans-serif; line-height: 1.6; }
.display-romance { font-family: 'Playfair Display', Georgia, serif; letter-spacing: 0.5px; }

.container { width: min(1000px, 92vw); margin: 0 auto; }
.center { display: grid; place-items: center; }

.btn { cursor: pointer; border: 0; border-radius: var(--radius-lg); padding: 14px 22px; font-weight: 600; color: var(--white); background: var(--primary); box-shadow: var(--shadow-soft); transition: transform .2s ease, box-shadow .2s ease; }
.btn:hover { transform: translateY(-1px); box-shadow: var(--shadow-elev); }

.fade-in { opacity: 0; transform: translateY(10px); transition: opacity .6s ease, transform .6s ease; }
.fade-in.revealed { opacity: 1; transform: translateY(0); }

/* Floating hearts for subtle romance background */
.hearts-bg { position: fixed; inset: 0; overflow: hidden; pointer-events: none; z-index: 0; }
.heart { position: absolute; width: 12px; height: 12px; background: var(--primary); transform: rotate(45deg); opacity: .15; border-radius: 2px; }
.heart::before, .heart::after { content: ''; position: absolute; width: 12px; height: 12px; background: var(--primary); border-radius: 50%; }
.heart::before { left: -6px; }
.heart::after { top: -6px; }
@keyframes floatUp { from { transform: translateY(0) rotate(45deg); } to { transform: translateY(-120vh) rotate(45deg); } }

