/* Font class for <PERSON> */
.ma-shan-zheng-regular {
  font-family: "<PERSON>", cursive;
  font-weight: 400;
  font-style: normal;
}

/* Proposal page styles */

/* New full-page split layout: top 4/5 image, bottom 1/5 caption */
.proposal-page {
  min-height: 100dvh;
  display: grid;
  grid-template-rows: 4fr 1fr;
  background: var(--bg);
}

.proposal-image {
  position: relative;
  overflow: hidden;
  display: grid;
  place-items: center;
}

.proposal-frame {
  width: 75vw; /* 3/4 of the screen width */
  max-width: 1000px;
  border-radius: 20px;
  overflow: hidden; /* clip image to rounded frame */
  box-shadow: var(--shadow-elev);
}

.proposal-frame img {
  display: block;
  width: 100%;
  height: auto; /* auto height based on intrinsic ratio */
}

.proposal-caption {
  display: grid;
  place-items: center;
  text-align: center;
  padding: 12px 16px;
  color: var(--ink-900);
}

.proposal-question {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.proposal-title {
  font-size: clamp(28px, 5vw, 48px);
  margin: 0;
  color: var(--primary);
  font-weight: 700;
}

.proposal-subtitle {
  font-size: clamp(16px, 3vw, 24px);
  margin: 0;
  opacity: 0.8;
}

/* Legacy styles retained in case JS references exist */
.yes-btn { font-size: 22px; padding: 16px 28px; background: linear-gradient(135deg, var(--primary), #ff8fb0); border-radius: 999px; box-shadow: 0 14px 36px rgba(214,51,108,.35); letter-spacing: .4px; }
.yes-btn:hover { transform: translateY(-2px) scale(1.02); }

.transition-layer { position: fixed; inset: 0; background: var(--rose-50); z-index: 10; opacity: 0; pointer-events: none; transition: opacity .6s ease; }
.transition-layer.show { opacity: 1; }

