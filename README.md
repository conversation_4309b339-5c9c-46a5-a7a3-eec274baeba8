## Romantic Proposal Website

A 3-page romantic website for a memorable proposal.

### Structure
- index.html — Landing page with envelope/heart button and floating letter decor
- timeline.html — Vertical timeline with dates on the left, image + text cards on the right
- proposal.html — Proposal page with big "Will you marry me?" and an irresistible YES button
- assets/css — Base, timeline, and proposal CSS
- assets/js — Shared utilities and per-page scripts
- data/timeline.json — Centralized content config for timeline items
- images/ — Put your photos here (update timeline.json paths)

### Customize Content
1. Replace placeholder images in `images/` and update paths in `data/timeline.json`.
2. Edit texts, titles, and dates in `data/timeline.json`.

Example `data/timeline.json` item:

```
{
  "date": "Jan 15, 2021",
  "title": "When Our Paths Crossed",
  "text": "The day we met, time slowed down. Your smile felt like home.",
  "image": "images/our-photo-1.jpg"
}
```

3. Landing copy is in `index.html` (CTA text) — feel free to personalize.
4. Colors and typography are set in `assets/css/base.css` (CSS variables at the top).

### How It Works
- index.html: Clicking the envelope navigates to timeline.html.
- timeline.html: Loads `data/timeline.json`, renders left-side date ticks and right-side cards, adds subtle reveal animations, smooth-scroll between entries when clicking date ticks.
- When the bottom is reached, a soft transition layer fades in and automatically navigates to `proposal.html`.
- proposal.html: Displays the question and a big YES button with a celebratory hearts burst.

### Development Tips
- Open `index.html` in a browser. If your browser blocks local JSON fetch, serve with a simple static server:
  - Python: `python3 -m http.server 5500`
  - Node: `npx serve -l 5500`
  - Then visit: `http://localhost:5500/index.html`
- Add or reorder timeline items by editing the array in `data/timeline.json`.
- Keep image sizes reasonable (e.g., 1600px wide) for smooth scrolling.

### Accessibility
- Semantic markup and aria labels where appropriate.
- Large, high-contrast CTA and YES button.

Best wishes and congratulations!

